
> discord-bot-nestjs@1.0.0 dev
> nest start --watch


 Info  Webpack is building your sources...

webpack 5.97.1 compiled [1m[32msuccessfully[39m[22m in 1418 ms
Type-checking in progress...
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[NestFactory] [39m[32mStarting Nest application...[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAppModule dependencies initialized[39m[38;5;3m +44ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mCoreModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mApiModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mPassportModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mFeaturesModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mMusicModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mGamingModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mUtilityModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mConfigHostModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mDiscoveryModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mTerminusModule dependencies initialized[39m[38;5;3m +1ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mConfigModule dependencies initialized[39m[38;5;3m +0ms[39m
[32m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mScheduleModule dependencies initialized[39m[38;5;3m +3ms[39m
[31m[Nest] 72542  - [39m07/24/2025, 7:13:46 PM [31m  ERROR[39m [38;5;3m[ExceptionHandler] [39m[31mCan't find meta/_journal.json file[39m
Error: Can't find meta/_journal.json file
    at readMigrationFiles (/home/<USER>/Discordbot-EnergeX/node_modules/.pnpm/drizzle-orm@0.33.0_@libsql+client@0.14.0_@opentelemetry+api@1.9.0_@types+pg@8.15.4_@typ_c9cc03a73461cbf5a690769a5429620b/node_modules/src/migrator.ts:41:9)
    at migrate (/home/<USER>/Discordbot-EnergeX/node_modules/.pnpm/drizzle-orm@0.33.0_@libsql+client@0.14.0_@opentelemetry+api@1.9.0_@types+pg@8.15.4_@typ_c9cc03a73461cbf5a690769a5429620b/node_modules/src/node-postgres/migrator.ts:9:21)
    at InstanceWrapper.useFactory [as metatype] (/home/<USER>/Discordbot-EnergeX/dist/src/main.js:4323:54)
    at Injector.instantiateClass (/home/<USER>/Discordbot-EnergeX/node_modules/.pnpm/@nestjs+core@10.4.20_@nestjs+common@10.4.20_class-transformer@0.5.1_class-validator@0.1_297e3f07848a68f9949b640773de1b75/node_modules/@nestjs/core/injector/injector.js:376:55)
    at callback (/home/<USER>/Discordbot-EnergeX/node_modules/.pnpm/@nestjs+core@10.4.20_@nestjs+common@10.4.20_class-transformer@0.5.1_class-validator@0.1_297e3f07848a68f9949b640773de1b75/node_modules/@nestjs/core/injector/injector.js:65:45)
    at async Injector.resolveConstructorParams (/home/<USER>/Discordbot-EnergeX/node_modules/.pnpm/@nestjs+core@10.4.20_@nestjs+common@10.4.20_class-transformer@0.5.1_class-validator@0.1_297e3f07848a68f9949b640773de1b75/node_modules/@nestjs/core/injector/injector.js:145:24)
    at async Injector.loadInstance (/home/<USER>/Discordbot-EnergeX/node_modules/.pnpm/@nestjs+core@10.4.20_@nestjs+common@10.4.20_class-transformer@0.5.1_class-validator@0.1_297e3f07848a68f9949b640773de1b75/node_modules/@nestjs/core/injector/injector.js:70:13)
    at async Injector.loadProvider (/home/<USER>/Discordbot-EnergeX/node_modules/.pnpm/@nestjs+core@10.4.20_@nestjs+common@10.4.20_class-transformer@0.5.1_class-validator@0.1_297e3f07848a68f9949b640773de1b75/node_modules/@nestjs/core/injector/injector.js:98:9)
    at async /home/<USER>/Discordbot-EnergeX/node_modules/.pnpm/@nestjs+core@10.4.20_@nestjs+common@10.4.20_class-transformer@0.5.1_class-validator@0.1_297e3f07848a68f9949b640773de1b75/node_modules/@nestjs/core/injector/instance-loader.js:56:13
    at async Promise.all (index 3)
ERROR in ./src/agents/agents.service.ts:100:13
TS2353: Object literal may only specify known properties, and 'accessCount' does not exist in type '{ userId?: string | SQL<unknown>; memoryType?: string | SQL<unknown>; key?: string | SQL<unknown>; value?: unknown; }'.
     98 |         await this.db.update(agentMemory)
     99 |           .set({ 
  > 100 |             accessCount: (memory.accessCount || 0) + 1,
        |             ^^^^^^^^^^^
    101 |             lastAccessedAt: new Date()
    102 |           })
    103 |           .where(eq(agentMemory.id, memory.id));

ERROR in ./src/agents/integration/ai-agent-integration.service.ts:190:54
TS2339: Property 'channels' does not exist on type 'unknown'.
    188 |
    189 |     // Check if the channel is in the configured channels list
  > 190 |     const configuredChannels = config.configuration?.channels || [];
        |                                                      ^^^^^^^^
    191 |     return configuredChannels.includes(message.channel.id) || configuredChannels.length === 0;
    192 |   }
    193 |

ERROR in ./src/agents/integration/channel-routing.service.ts:187:63
TS2322: Type 'unknown' is not assignable to type 'object'.
    185 |       const config = await this.agentsService.getAgentConfig(guildId, 'general');
    186 |       
  > 187 |       if (config && config.configuration && 'defaultAgent' in config.configuration) {
        |                                                               ^^^^^^^^^^^^^^^^^^^^
    188 |         return (config.configuration as any).defaultAgent;
    189 |       }
    190 |       

ERROR in ./src/agents/types/intake-specialist.ts:328:43
TS2339: Property 'response' does not exist on type 'unknown'.
    326 |         const memory = await this.agentsService.getMemory(userId, `intake_${stage}`);
    327 |         if (memory) {
  > 328 |           responses[stage] = memory.value.response;
        |                                           ^^^^^^^^
    329 |         }
    330 |       }
    331 |       

ERROR in ./src/agents/types/progress-tracker.ts:291:42
TS2339: Property 'completed' does not exist on type 'unknown'.
    289 |         hasData: !!(goals || progress),
    290 |         goals: goals?.value || [],
  > 291 |         completedGoals: progress?.value?.completed || 0,
        |                                          ^^^^^^^^^
    292 |         activeGoals: progress?.value?.active || 0,
    293 |         streakDays: progress?.value?.streak || 0,
    294 |         weeklyProgress: progress?.value?.weeklyPercent || 0,

ERROR in ./src/agents/types/progress-tracker.ts:292:39
TS2339: Property 'active' does not exist on type 'unknown'.
    290 |         goals: goals?.value || [],
    291 |         completedGoals: progress?.value?.completed || 0,
  > 292 |         activeGoals: progress?.value?.active || 0,
        |                                       ^^^^^^
    293 |         streakDays: progress?.value?.streak || 0,
    294 |         weeklyProgress: progress?.value?.weeklyPercent || 0,
    295 |         overallProgress: progress?.value?.overall || 'Starting journey',

ERROR in ./src/agents/types/progress-tracker.ts:293:38
TS2339: Property 'streak' does not exist on type 'unknown'.
    291 |         completedGoals: progress?.value?.completed || 0,
    292 |         activeGoals: progress?.value?.active || 0,
  > 293 |         streakDays: progress?.value?.streak || 0,
        |                                      ^^^^^^
    294 |         weeklyProgress: progress?.value?.weeklyPercent || 0,
    295 |         overallProgress: progress?.value?.overall || 'Starting journey',
    296 |         trends: trends?.value || { improving: true, challenges: false },

ERROR in ./src/agents/types/progress-tracker.ts:294:42
TS2339: Property 'weeklyPercent' does not exist on type 'unknown'.
    292 |         activeGoals: progress?.value?.active || 0,
    293 |         streakDays: progress?.value?.streak || 0,
  > 294 |         weeklyProgress: progress?.value?.weeklyPercent || 0,
        |                                          ^^^^^^^^^^^^^
    295 |         overallProgress: progress?.value?.overall || 'Starting journey',
    296 |         trends: trends?.value || { improving: true, challenges: false },
    297 |       };

ERROR in ./src/agents/types/progress-tracker.ts:295:43
TS2339: Property 'overall' does not exist on type 'unknown'.
    293 |         streakDays: progress?.value?.streak || 0,
    294 |         weeklyProgress: progress?.value?.weeklyPercent || 0,
  > 295 |         overallProgress: progress?.value?.overall || 'Starting journey',
        |                                           ^^^^^^^
    296 |         trends: trends?.value || { improving: true, challenges: false },
    297 |       };
    298 |     } catch (error) {

ERROR in ./src/agents/types/progress-tracker.ts:343:15
TS2339: Property 'totalCheckins' does not exist on type 'unknown'.
    341 |       };
    342 |
  > 343 |       metrics.totalCheckins += 1;
        |               ^^^^^^^^^^^^^
    344 |       metrics.lastCheckin = new Date().toISOString();
    345 |       
    346 |       // Reset weekly counter if it's a new week

ERROR in ./src/agents/types/progress-tracker.ts:344:15
TS2339: Property 'lastCheckin' does not exist on type 'unknown'.
    342 |
    343 |       metrics.totalCheckins += 1;
  > 344 |       metrics.lastCheckin = new Date().toISOString();
        |               ^^^^^^^^^^^
    345 |       
    346 |       // Reset weekly counter if it's a new week
    347 |       const lastWeek = new Date(metrics.lastCheckin || 0);

ERROR in ./src/agents/types/progress-tracker.ts:347:41
TS2339: Property 'lastCheckin' does not exist on type 'unknown'.
    345 |       
    346 |       // Reset weekly counter if it's a new week
  > 347 |       const lastWeek = new Date(metrics.lastCheckin || 0);
        |                                         ^^^^^^^^^^^
    348 |       const now = new Date();
    349 |       if (now.getTime() - lastWeek.getTime() > 7 * 24 * 60 * 60 * 1000) {
    350 |         metrics.weeklyCheckins = 1;

ERROR in ./src/agents/types/progress-tracker.ts:350:17
TS2339: Property 'weeklyCheckins' does not exist on type 'unknown'.
    348 |       const now = new Date();
    349 |       if (now.getTime() - lastWeek.getTime() > 7 * 24 * 60 * 60 * 1000) {
  > 350 |         metrics.weeklyCheckins = 1;
        |                 ^^^^^^^^^^^^^^
    351 |       } else {
    352 |         metrics.weeklyCheckins += 1;
    353 |       }

ERROR in ./src/agents/types/progress-tracker.ts:352:17
TS2339: Property 'weeklyCheckins' does not exist on type 'unknown'.
    350 |         metrics.weeklyCheckins = 1;
    351 |       } else {
  > 352 |         metrics.weeklyCheckins += 1;
        |                 ^^^^^^^^^^^^^^
    353 |       }
    354 |
    355 |       await this.agentsService.storeMemory({

ERROR in ./src/api/admin/components/analytics.service.ts:151:14
TS2339: Property 'interactionRepository' does not exist on type 'AnalyticsService'.
    149 |         interactionTypes
    150 |       ] = await Promise.all([
  > 151 |         this.interactionRepository.count(),
        |              ^^^^^^^^^^^^^^^^^^^^^
    152 |         this.interactionRepository.count({ where: { createdAt: { $gte: oneDayAgo } as any } }),
    153 |         this.interactionRepository.count({ where: { createdAt: { $gte: oneWeekAgo } as any } }),
    154 |         this.getAgentInteractionBreakdown(),

ERROR in ./src/api/admin/components/analytics.service.ts:152:14
TS2339: Property 'interactionRepository' does not exist on type 'AnalyticsService'.
    150 |       ] = await Promise.all([
    151 |         this.interactionRepository.count(),
  > 152 |         this.interactionRepository.count({ where: { createdAt: { $gte: oneDayAgo } as any } }),
        |              ^^^^^^^^^^^^^^^^^^^^^
    153 |         this.interactionRepository.count({ where: { createdAt: { $gte: oneWeekAgo } as any } }),
    154 |         this.getAgentInteractionBreakdown(),
    155 |         this.getInteractionTypeBreakdown(),

ERROR in ./src/api/admin/components/analytics.service.ts:153:14
TS2339: Property 'interactionRepository' does not exist on type 'AnalyticsService'.
    151 |         this.interactionRepository.count(),
    152 |         this.interactionRepository.count({ where: { createdAt: { $gte: oneDayAgo } as any } }),
  > 153 |         this.interactionRepository.count({ where: { createdAt: { $gte: oneWeekAgo } as any } }),
        |              ^^^^^^^^^^^^^^^^^^^^^
    154 |         this.getAgentInteractionBreakdown(),
    155 |         this.getInteractionTypeBreakdown(),
    156 |       ]);

ERROR in ./src/api/admin/components/analytics.service.ts:180:25
TS2339: Property 'userRepository' does not exist on type 'AnalyticsService'.
    178 |     // In a real implementation, this would query daily registration counts
    179 |     return {
  > 180 |       today: await this.userRepository.count({ where: { createdAt: { $gte: periods.today } as any } }),
        |                         ^^^^^^^^^^^^^^
    181 |       week: await this.userRepository.count({ where: { createdAt: { $gte: periods.week } as any } }),
    182 |       month: await this.userRepository.count({ where: { createdAt: { $gte: periods.month } as any } }),
    183 |     };

ERROR in ./src/api/admin/components/analytics.service.ts:181:24
TS2339: Property 'userRepository' does not exist on type 'AnalyticsService'.
    179 |     return {
    180 |       today: await this.userRepository.count({ where: { createdAt: { $gte: periods.today } as any } }),
  > 181 |       week: await this.userRepository.count({ where: { createdAt: { $gte: periods.week } as any } }),
        |                        ^^^^^^^^^^^^^^
    182 |       month: await this.userRepository.count({ where: { createdAt: { $gte: periods.month } as any } }),
    183 |     };
    184 |   }

ERROR in ./src/api/admin/components/analytics.service.ts:182:25
TS2339: Property 'userRepository' does not exist on type 'AnalyticsService'.
    180 |       today: await this.userRepository.count({ where: { createdAt: { $gte: periods.today } as any } }),
    181 |       week: await this.userRepository.count({ where: { createdAt: { $gte: periods.week } as any } }),
  > 182 |       month: await this.userRepository.count({ where: { createdAt: { $gte: periods.month } as any } }),
        |                         ^^^^^^^^^^^^^^
    183 |     };
    184 |   }
    185 |

ERROR in ./src/api/admin/components/analytics.service.ts:187:36
TS2339: Property 'userRepository' does not exist on type 'AnalyticsService'.
    185 |
    186 |   private async getUserActivityPatterns() {
  > 187 |     const activeUsers = await this.userRepository.count({ where: { isActive: true } });
        |                                    ^^^^^^^^^^^^^^
    188 |     const totalUsers = await this.userRepository.count();
    189 |     
    190 |     return {

ERROR in ./src/api/admin/components/analytics.service.ts:188:35
TS2339: Property 'userRepository' does not exist on type 'AnalyticsService'.
    186 |   private async getUserActivityPatterns() {
    187 |     const activeUsers = await this.userRepository.count({ where: { isActive: true } });
  > 188 |     const totalUsers = await this.userRepository.count();
        |                                   ^^^^^^^^^^^^^^
    189 |     
    190 |     return {
    191 |       activeUsers,

ERROR in ./src/api/admin/components/analytics.service.ts:211:30
TS2339: Property 'userRepository' does not exist on type 'AnalyticsService'.
    209 |   private async getTopActiveUsers() {
    210 |     // This would typically join with interaction data
  > 211 |     const users = await this.userRepository.find({
        |                              ^^^^^^^^^^^^^^
    212 |       where: { isActive: true },
    213 |       take: 10,
    214 |       order: { lastActivityAt: 'DESC' },

ERROR in ./src/api/admin/components/analytics.service.ts:227:31
TS2339: Property 'guildRepository' does not exist on type 'AnalyticsService'.
    225 |
    226 |   private async getGuildSizeDistribution() {
  > 227 |     const guilds = await this.guildRepository.find();
        |                               ^^^^^^^^^^^^^^^
    228 |     
    229 |     // Simulate guild size distribution
    230 |     const distribution = {

ERROR in ./src/api/admin/components/analytics.service.ts:252:36
TS2339: Property 'guildRepository' does not exist on type 'AnalyticsService'.
    250 |
    251 |   private async getGuildActivityStats() {
  > 252 |     const totalGuilds = await this.guildRepository.count();
        |                                    ^^^^^^^^^^^^^^^
    253 |     const activeGuilds = await this.guildRepository.count({ where: { isActive: true } });
    254 |
    255 |     return {

ERROR in ./src/api/admin/components/analytics.service.ts:253:37
TS2339: Property 'guildRepository' does not exist on type 'AnalyticsService'.
    251 |   private async getGuildActivityStats() {
    252 |     const totalGuilds = await this.guildRepository.count();
  > 253 |     const activeGuilds = await this.guildRepository.count({ where: { isActive: true } });
        |                                     ^^^^^^^^^^^^^^^
    254 |
    255 |     return {
    256 |       totalGuilds,

ERROR in ./src/api/admin/components/analytics.service.ts:264:31
TS2339: Property 'guildRepository' does not exist on type 'AnalyticsService'.
    262 |
    263 |   private async getTopGuildsByActivity() {
  > 264 |     const guilds = await this.guildRepository.find({
        |                               ^^^^^^^^^^^^^^^
    265 |       where: { isActive: true },
    266 |       take: 10,
    267 |       order: { lastActivityAt: 'DESC' },

ERROR in ./src/api/admin/components/system-health.service.ts:26:14
TS2339: Property 'userRepository' does not exist on type 'SystemHealthService'.
    24 |         uptime
    25 |       ] = await Promise.all([
  > 26 |         this.userRepository.count(),
       |              ^^^^^^^^^^^^^^
    27 |         this.userRepository.count({ where: { isActive: true } }),
    28 |         this.guildRepository.count(),
    29 |         this.sessionRepository.count({ where: { isRevoked: false } }),

ERROR in ./src/api/admin/components/system-health.service.ts:27:14
TS2339: Property 'userRepository' does not exist on type 'SystemHealthService'.
    25 |       ] = await Promise.all([
    26 |         this.userRepository.count(),
  > 27 |         this.userRepository.count({ where: { isActive: true } }),
       |              ^^^^^^^^^^^^^^
    28 |         this.guildRepository.count(),
    29 |         this.sessionRepository.count({ where: { isRevoked: false } }),
    30 |         this.getMemoryUsage(),

ERROR in ./src/api/admin/components/system-health.service.ts:28:14
TS2339: Property 'guildRepository' does not exist on type 'SystemHealthService'.
    26 |         this.userRepository.count(),
    27 |         this.userRepository.count({ where: { isActive: true } }),
  > 28 |         this.guildRepository.count(),
       |              ^^^^^^^^^^^^^^^
    29 |         this.sessionRepository.count({ where: { isRevoked: false } }),
    30 |         this.getMemoryUsage(),
    31 |         process.uptime()

ERROR in ./src/api/admin/components/system-health.service.ts:29:14
TS2339: Property 'sessionRepository' does not exist on type 'SystemHealthService'.
    27 |         this.userRepository.count({ where: { isActive: true } }),
    28 |         this.guildRepository.count(),
  > 29 |         this.sessionRepository.count({ where: { isRevoked: false } }),
       |              ^^^^^^^^^^^^^^^^^
    30 |         this.getMemoryUsage(),
    31 |         process.uptime()
    32 |       ]);

ERROR in ./src/api/admin/components/system-health.service.ts:92:14
TS2339: Property 'userRepository' does not exist on type 'SystemHealthService'.
    90 |         memoryTrend
    91 |       ] = await Promise.all([
  > 92 |         this.userRepository.count({
       |              ^^^^^^^^^^^^^^
    93 |           where: { createdAt: { $gte: oneHourAgo } as any },
    94 |         }),
    95 |         this.sessionRepository.count({

ERROR in ./src/api/admin/components/system-health.service.ts:95:14
TS2339: Property 'sessionRepository' does not exist on type 'SystemHealthService'.
    93 |           where: { createdAt: { $gte: oneHourAgo } as any },
    94 |         }),
  > 95 |         this.sessionRepository.count({
       |              ^^^^^^^^^^^^^^^^^
    96 |           where: { createdAt: { $gte: oneHourAgo } as any },
    97 |         }),
    98 |         this.getRequestCount(),

ERROR in ./src/api/admin/components/system-health.service.ts:107:36
TS2339: Property 'userRepository' does not exist on type 'SystemHealthService'.
    105 |           users: {
    106 |             newLastHour: recentUsers,
  > 107 |             newLast24h: await this.userRepository.count({
        |                                    ^^^^^^^^^^^^^^
    108 |               where: { createdAt: { $gte: oneDayAgo } as any },
    109 |             }),
    110 |           },

ERROR in ./src/api/admin/components/system-health.service.ts:113:37
TS2339: Property 'sessionRepository' does not exist on type 'SystemHealthService'.
    111 |           sessions: {
    112 |             newLastHour: recentSessions,
  > 113 |             activeTotal: await this.sessionRepository.count({
        |                                     ^^^^^^^^^^^^^^^^^
    114 |               where: { isRevoked: false },
    115 |             }),
    116 |           },

ERROR in ./src/api/admin/components/system-health.service.ts:188:18
TS2339: Property 'userRepository' does not exist on type 'SystemHealthService'.
    186 |     // Database connection health (simplified check)
    187 |     try {
  > 188 |       await this.userRepository.count();
        |                  ^^^^^^^^^^^^^^
    189 |     } catch (error) {
    190 |       alerts.push({
    191 |         id: 'database-connection',

ERROR in ./src/api/auth/auth.controller.ts:73:22
TS2339: Property 'user' does not exist on type '{ id: number; sessionId: string; userId: string; encryptedData: string; expiresAt: Date; ipAddress: string; userAgent: string; deviceFingerprint: string; isRevoked: boolean; lastAccessedAt: Date; metadata: unknown; createdAt: Date; updatedAt: Date; deletedAt: Date; }'.
    71 |     return {
    72 |       authenticated: !!session,
  > 73 |       user: session?.user || null,
       |                      ^^^^
    74 |       session: session ? {
    75 |         id: session.sessionId,
    76 |         expiresAt: session.expiresAt,

ERROR in ./src/api/guilds/guilds.service.ts:69:11
TS2353: Object literal may only specify known properties, and 'features' does not exist in type '{ name: string; discordId: string; }'.
    67 |           discordId: guildId,
    68 |           name: 'Unknown Guild',
  > 69 |           features: { [feature]: config },
       |           ^^^^^^^^
    70 |           isActive: true,
    71 |         };
    72 |         const insertResult = await this.db.insert(guilds).values(newGuild).returning();

ERROR in ./src/api/guilds/guilds.service.ts:76:11
TS2698: Spread types may only be created from object types.
    74 |       } else {
    75 |         const updatedFeatures = {
  > 76 |           ...guild.features,
       |           ^^^^^^^^^^^^^^^^^
    77 |           [feature]: config,
    78 |         };
    79 |         await this.db.update(guilds).set({ features: updatedFeatures }).where(eq(guilds.discordId, guildId));

ERROR in ./src/api/guilds/guilds.service.ts:79:44
TS2353: Object literal may only specify known properties, and 'features' does not exist in type '{ name?: string | SQL<unknown>; discordId?: string | SQL<unknown>; }'.
    77 |           [feature]: config,
    78 |         };
  > 79 |         await this.db.update(guilds).set({ features: updatedFeatures }).where(eq(guilds.discordId, guildId));
       |                                            ^^^^^^^^
    80 |         guild = { ...guild, features: updatedFeatures };
    81 |       }
    82 |

ERROR in ./src/core/database/database.service.ts:24:58
TS2554: Expected 1 arguments, but got 2.
    22 |   async query<T = any>(text: string, params?: any[]): Promise<T[]> {
    23 |     try {
  > 24 |       const result = await this.db.execute(sql.raw(text, params));
       |                                                          ^^^^^^
    25 |       return result.rows as T[];
    26 |     } catch (error) {
    27 |       this.logger.error(`Database query failed: ${text}`, error);

ERROR in ./src/core/security/session.service.ts:3:20
TS2459: Module '"../database/entities/session.entity"' declares 'users' locally, but it is not exported.
    1 | import { Injectable, Logger, Inject } from '@nestjs/common';
    2 | import { DatabaseService, DATABASE_CONNECTION } from '../database/database.service';
  > 3 | import { sessions, users, Session, NewSession } from '../database/entities/session.entity';
      |                    ^^^^^
    4 | import { EncryptionService } from './encryption.service';
    5 | import { Request } from 'express';
    6 | import { eq, and, lt } from 'drizzle-orm';

ERROR in ./src/core/security/session.service.ts:38:9
TS2353: Object literal may only specify known properties, and 'ipAddress' does not exist in type '{ sessionId: string; userId: string; expiresAt: Date; }'.
    36 |         userId,
    37 |         expiresAt,
  > 38 |         ipAddress,
       |         ^^^^^^^^^
    39 |         userAgent,
    40 |         deviceFingerprint,
    41 |         lastAccessedAt: new Date(),

ERROR in ./src/core/security/session.service.ts:79:16
TS2353: Object literal may only specify known properties, and 'lastAccessedAt' does not exist in type '{ sessionId?: string | SQL<unknown>; userId?: string | SQL<unknown>; expiresAt?: Date | SQL<unknown>; }'.
    77 |       await this.db
    78 |         .update(sessions)
  > 79 |         .set({ lastAccessedAt: new Date() })
       |                ^^^^^^^^^^^^^^
    80 |         .where(eq(sessions.sessionId, sessionId));
    81 |
    82 |       return { ...session, lastAccessedAt: new Date() };

ERROR in ./src/core/security/session.service.ts:93:16
TS2353: Object literal may only specify known properties, and 'isRevoked' does not exist in type '{ sessionId?: string | SQL<unknown>; userId?: string | SQL<unknown>; expiresAt?: Date | SQL<unknown>; }'.
    91 |       await this.db
    92 |         .update(sessions)
  > 93 |         .set({ isRevoked: true })
       |                ^^^^^^^^^
    94 |         .where(eq(sessions.sessionId, sessionId));
    95 |     } catch (error) {
    96 |       this.logger.error('Failed to revoke session:', error);

ERROR in ./src/core/security/user.service.ts:39:9
TS2353: Object literal may only specify known properties, and 'email' does not exist in type '{ discordId: string; username: string; }'.
    37 |         discordId: userData.discordId,
    38 |         username: userData.username,
  > 39 |         email: userData.email,
       |         ^^^^^
    40 |         accessToken: userData.accessToken ? 
    41 |           this.encryptionService.encryptUserData(userData.accessToken) : undefined,
    42 |         refreshToken: userData.refreshToken ? 

ERROR in ./src/discord/discord.service.ts:62:32
TS2339: Property 'guildRepository' does not exist on type 'DiscordService'.
    60 |     try {
    61 |       // Get guild configuration
  > 62 |       const guild = await this.guildRepository.findOne({
       |                                ^^^^^^^^^^^^^^^
    63 |         where: { discordId: guildId },
    64 |       });
    65 |

ERROR in ./src/discord/discord.service.ts:71:41
TS2339: Property 'aiAgentConfigRepository' does not exist on type 'DiscordService'.
    69 |
    70 |       // Check AI agents configuration
  > 71 |       const aiAgentConfigs = await this.aiAgentConfigRepository.find({
       |                                         ^^^^^^^^^^^^^^^^^^^^^^^
    72 |         where: { guildId, enabled: true },
    73 |       });
    74 |       

ERROR in ./src/discord/events/discord-events.service.ts:95:9
TS2353: Object literal may only specify known properties, and 'icon' does not exist in type '{ name: string; discordId: string; }'.
    93 |         discordId: guild.id,
    94 |         name: guild.name,
  > 95 |         icon: guild.iconURL() || undefined,
       |         ^^^^
    96 |         ownerDiscordId: guild.ownerId,
    97 |         isActive: true,
    98 |         lastActivityAt: new Date(),

ERROR in ./src/discord/events/discord-events.service.ts:106:13
TS2353: Object literal may only specify known properties, and 'icon' does not exist in type 'PgUpdateSetSource<PgTableWithColumns<{ name: "guilds"; schema: undefined; columns: { discordId: PgColumn<{ name: "discord_id"; tableName: "guilds"; dataType: "string"; columnType: "PgVarchar"; data: string; driverParam: string; notNull: true; ... 6 more ...; generated: GeneratedColumnConfig<...>; }, {}, {}>; ... 18 ...'.
    104 |           set: {
    105 |             name: guildData.name,
  > 106 |             icon: guildData.icon,
        |             ^^^^
    107 |             ownerDiscordId: guildData.ownerDiscordId,
    108 |             isActive: guildData.isActive,
    109 |             lastActivityAt: guildData.lastActivityAt,

ERROR in ./src/discord/events/discord-events.service.ts:106:29
TS2339: Property 'icon' does not exist on type '{ name: string; discordId: string; }'.
    104 |           set: {
    105 |             name: guildData.name,
  > 106 |             icon: guildData.icon,
        |                             ^^^^
    107 |             ownerDiscordId: guildData.ownerDiscordId,
    108 |             isActive: guildData.isActive,
    109 |             lastActivityAt: guildData.lastActivityAt,

ERROR in ./src/discord/events/discord-events.service.ts:107:39
TS2339: Property 'ownerDiscordId' does not exist on type '{ name: string; discordId: string; }'.
    105 |             name: guildData.name,
    106 |             icon: guildData.icon,
  > 107 |             ownerDiscordId: guildData.ownerDiscordId,
        |                                       ^^^^^^^^^^^^^^
    108 |             isActive: guildData.isActive,
    109 |             lastActivityAt: guildData.lastActivityAt,
    110 |           }

ERROR in ./src/discord/events/discord-events.service.ts:108:33
TS2339: Property 'isActive' does not exist on type '{ name: string; discordId: string; }'.
    106 |             icon: guildData.icon,
    107 |             ownerDiscordId: guildData.ownerDiscordId,
  > 108 |             isActive: guildData.isActive,
        |                                 ^^^^^^^^
    109 |             lastActivityAt: guildData.lastActivityAt,
    110 |           }
    111 |         });

ERROR in ./src/discord/events/discord-events.service.ts:109:39
TS2339: Property 'lastActivityAt' does not exist on type '{ name: string; discordId: string; }'.
    107 |             ownerDiscordId: guildData.ownerDiscordId,
    108 |             isActive: guildData.isActive,
  > 109 |             lastActivityAt: guildData.lastActivityAt,
        |                                       ^^^^^^^^^^^^^^
    110 |           }
    111 |         });
    112 |     } catch (error) {

ERROR in ./src/discord/events/discord-events.service.ts:124:16
TS2353: Object literal may only specify known properties, and 'isActive' does not exist in type '{ name?: string | SQL<unknown>; discordId?: string | SQL<unknown>; }'.
    122 |       await this.db
    123 |         .update(guilds)
  > 124 |         .set({ isActive, lastActivityAt: new Date() })
        |                ^^^^^^^^
    125 |         .where(eq(guilds.discordId, guildId));
    126 |     } catch (error) {
    127 |       this.logger.error(`Failed to update guild ${guildId} status:`, error);

ERROR in ./src/discord/events/discord-events.service.ts:140:11
TS2353: Object literal may only specify known properties, and 'icon' does not exist in type '{ name?: string | SQL<unknown>; discordId?: string | SQL<unknown>; }'.
    138 |         .set({
    139 |           name: guild.name,
  > 140 |           icon: guild.iconURL() || undefined,
        |           ^^^^
    141 |           ownerDiscordId: guild.ownerId,
    142 |           lastActivityAt: new Date(),
    143 |         })

ERROR in ./src/discord/events/discord-events.service.ts:158:9
TS2353: Object literal may only specify known properties, and 'isActive' does not exist in type '{ discordId: string; username: string; }'.
    156 |         discordId: user.id,
    157 |         username: user.username,
  > 158 |         isActive: true,
        |         ^^^^^^^^
    159 |         lastActivityAt: new Date(),
    160 |       };
    161 |

ERROR in ./src/discord/events/discord-events.service.ts:167:13
TS2353: Object literal may only specify known properties, and 'isActive' does not exist in type 'PgUpdateSetSource<PgTableWithColumns<{ name: "users"; schema: undefined; columns: { discordId: PgColumn<{ name: "discord_id"; tableName: "users"; dataType: "string"; columnType: "PgVarchar"; data: string; driverParam: string; notNull: true; ... 6 more ...; generated: GeneratedColumnConfig<...>; }, {}, {}>; ... 17 mo...'.
    165 |           set: {
    166 |             username: userData.username,
  > 167 |             isActive: userData.isActive,
        |             ^^^^^^^^
    168 |             lastActivityAt: userData.lastActivityAt,
    169 |           }
    170 |         });

ERROR in ./src/discord/events/discord-events.service.ts:167:32
TS2339: Property 'isActive' does not exist on type '{ discordId: string; username: string; }'.
    165 |           set: {
    166 |             username: userData.username,
  > 167 |             isActive: userData.isActive,
        |                                ^^^^^^^^
    168 |             lastActivityAt: userData.lastActivityAt,
    169 |           }
    170 |         });

ERROR in ./src/discord/events/discord-events.service.ts:168:38
TS2339: Property 'lastActivityAt' does not exist on type '{ discordId: string; username: string; }'.
    166 |             username: userData.username,
    167 |             isActive: userData.isActive,
  > 168 |             lastActivityAt: userData.lastActivityAt,
        |                                      ^^^^^^^^^^^^^^
    169 |           }
    170 |         });
    171 |     } catch (error) {

ERROR in ./src/features/ai-automation/ai-automation.service.ts:59:62
TS2769: No overload matches this call.
  Overload 1 of 2, '(value: { name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }): PgInsertBase<PgTableWithColumns<{ name: "guilds"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'settings' does not exist in type '{ name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }'.
  Overload 2 of 2, '(values: { name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }[]): PgInsertBase<PgTableWithColumns<{ name: "guilds"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'discordId' does not exist in type '{ name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }[]'.
    57 |
    58 |       if (!guild) {
  > 59 |         const newGuildResults = await this.db.insert(guilds).values({
       |                                                              ^^^^^^
    60 |           discordId: interaction.guild.id,
    61 |           name: interaction.guild.name,
    62 |           settings: { aiAutomation: {} },

ERROR in ./src/features/ai-automation/ai-automation.service.ts:67:42
TS2339: Property 'aiAutomation' does not exist on type 'unknown'.
    65 |       }
    66 |
  > 67 |       const aiSettings = guild.settings?.aiAutomation || {};
       |                                          ^^^^^^^^^^^^
    68 |       aiSettings[feature] = enabled;
    69 |
    70 |       const updatedSettings = { ...guild.settings, aiAutomation: aiSettings };

ERROR in ./src/features/ai-automation/ai-automation.service.ts:70:33
TS2698: Spread types may only be created from object types.
    68 |       aiSettings[feature] = enabled;
    69 |
  > 70 |       const updatedSettings = { ...guild.settings, aiAutomation: aiSettings };
       |                                 ^^^^^^^^^^^^^^^^^
    71 |       await this.db.update(guilds).set({ settings: updatedSettings }).where(eq(guilds.id, guild.id));
    72 |
    73 |       const featureNames = {

ERROR in ./src/features/ai-automation/ai-automation.service.ts:71:42
TS2353: Object literal may only specify known properties, and 'settings' does not exist in type '{ name?: string | SQL<unknown>; discordId?: string | SQL<unknown>; }'.
    69 |
    70 |       const updatedSettings = { ...guild.settings, aiAutomation: aiSettings };
  > 71 |       await this.db.update(guilds).set({ settings: updatedSettings }).where(eq(guilds.id, guild.id));
       |                                          ^^^^^^^^
    72 |
    73 |       const featureNames = {
    74 |         smart_greetings: 'Smart Greetings',

ERROR in ./src/features/ai-automation/ai-automation.service.ts:115:29
TS2339: Property 'aiAutomation' does not exist on type 'unknown'.
    113 |       const guild = guildResults[0];
    114 |
  > 115 |       if (!guild?.settings?.aiAutomation?.smart_greetings) return;
        |                             ^^^^^^^^^^^^
    116 |
    117 |       // Generate personalized greeting based on user profile
    118 |       const greeting = await this.generateSmartGreeting(member);

ERROR in ./src/features/ai-automation/ai-automation.service.ts:143:29
TS2339: Property 'aiAutomation' does not exist on type 'unknown'.
    141 |       const guild = guildResults[0];
    142 |
  > 143 |       if (!guild?.settings?.aiAutomation?.auto_engagement) return;
        |                             ^^^^^^^^^^^^
    144 |
    145 |       // Analyze message for engagement opportunities
    146 |       const shouldEngage = await this.analyzeEngagementOpportunity(message);

ERROR in ./src/features/ai-automation/ai-automation.service.ts:169:30
TS2339: Property 'aiAutomation' does not exist on type 'unknown'.
    167 |
    168 |       for (const guild of activeGuilds) {
  > 169 |         if (!guild.settings?.aiAutomation?.content_suggestions) continue;
        |                              ^^^^^^^^^^^^
    170 |
    171 |         const suggestions = await this.analyzeAndSuggestContent(guild.discordId);
    172 |         

ERROR in ./src/features/ai-automation/ai-automation.service.ts:190:30
TS2339: Property 'aiAutomation' does not exist on type 'unknown'.
    188 |
    189 |       for (const guild of activeGuilds) {
  > 190 |         if (!guild.settings?.aiAutomation?.member_insights) continue;
        |                              ^^^^^^^^^^^^
    191 |
    192 |         const insights = await this.analyzeMemberActivity(guild.discordId);
    193 |         

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:37:29
TS2339: Property 'aiAutomation' does not exist on type 'unknown'.
    35 |       const guild = guildResults[0];
    36 |
  > 37 |       if (!guild?.settings?.aiAutomation?.auto_moderation) return;
       |                             ^^^^^^^^^^^^
    38 |
    39 |       const analysis = await this.analyzeMessage(message);
    40 |       

ERROR in ./src/features/ai-automation/auto-moderation.service.ts:242:45
TS2339: Property 'moderation' does not exist on type 'unknown'.
    240 |       const guild = guildResults[0];
    241 |
  > 242 |       const logChannelId = guild?.settings?.moderation?.logChannel;
        |                                             ^^^^^^^^^^
    243 |       if (logChannelId) {
    244 |         const logChannel = message.guild!.channels.cache.get(logChannelId);
    245 |         if (logChannel?.isTextBased()) {

ERROR in ./src/features/ai-automation/smart-notification.service.ts:22:30
TS2339: Property 'aiAutomation' does not exist on type 'unknown'.
    20 |
    21 |       for (const guild of activeGuilds) {
  > 22 |         if (!guild.settings?.aiAutomation?.smart_notifications) continue;
       |                              ^^^^^^^^^^^^
    23 |
    24 |         await this.identifyAndNotifyInactiveUsers(guild.discordId);
    25 |       }

ERROR in ./src/features/ai-automation/smart-notification.service.ts:39:30
TS2339: Property 'aiAutomation' does not exist on type 'unknown'.
    37 |
    38 |       for (const guild of activeGuilds) {
  > 39 |         if (!guild.settings?.aiAutomation?.engagement_digest) continue;
       |                              ^^^^^^^^^^^^
    40 |
    41 |         await this.generateEngagementDigest(guild.discordId);
    42 |       }

ERROR in ./src/features/ai-automation/smart-notification.service.ts:56:30
TS2339: Property 'aiAutomation' does not exist on type 'unknown'.
    54 |
    55 |       for (const guild of activeGuilds) {
  > 56 |         if (!guild.settings?.aiAutomation?.weekly_insights) continue;
       |                              ^^^^^^^^^^^^
    57 |
    58 |         await this.generateWeeklyInsights(guild.discordId);
    59 |       }

ERROR in ./src/features/ai-automation/smart-notification.service.ts:183:54
TS2339: Property 'topic' does not exist on type 'unknown'.
    181 |
    182 |     if (userInteractions.length > 0) {
  > 183 |       const lastTopic = userInteractions[0].context?.topic || 'community discussions';
        |                                                      ^^^^^
    184 |       return `Hi ${user.username}! There have been some new developments in ${lastTopic} that you might find interesting. Come check it out! 😊`;
    185 |     }
    186 |

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:92:62
TS2769: No overload matches this call.
  Overload 1 of 2, '(value: { name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }): PgInsertBase<PgTableWithColumns<{ name: "guilds"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'settings' does not exist in type '{ name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }'.
  Overload 2 of 2, '(values: { name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }[]): PgInsertBase<PgTableWithColumns<{ name: "guilds"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'discordId' does not exist in type '{ name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }[]'.
    90 |
    91 |       if (!guild) {
  > 92 |         const newGuildResults = await this.db.insert(guilds).values({
       |                                                              ^^^^^^
    93 |           discordId: interaction.guild.id,
    94 |           name: interaction.guild.name,
    95 |           settings: { devOnDemand: { enabled: false } },

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:135:28
TS2339: Property 'devOnDemand' does not exist on type 'unknown'.
    133 |
    134 |       // Initialize settings
  > 135 |       if (!guild.settings?.devOnDemand) {
        |                            ^^^^^^^^^^^
    136 |         guild.settings = {
    137 |           ...guild.settings,
    138 |           devOnDemand: {

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:137:11
TS2698: Spread types may only be created from object types.
    135 |       if (!guild.settings?.devOnDemand) {
    136 |         guild.settings = {
  > 137 |           ...guild.settings,
        |           ^^^^^^^^^^^^^^^^^
    138 |           devOnDemand: {
    139 |             enabled: false,
    140 |             requestChannel: null,

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:149:44
TS2353: Object literal may only specify known properties, and 'settings' does not exist in type '{ name?: string | SQL<unknown>; discordId?: string | SQL<unknown>; }'.
    147 |           },
    148 |         };
  > 149 |         await this.db.update(guilds).set({ settings: guild.settings }).where(eq(guilds.id, guild.id));
        |                                            ^^^^^^^^
    150 |       }
    151 |     } catch (error) {
    152 |       this.logger.error('Failed to set up dev-on-demand:', error);

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:180:44
TS2339: Property 'devOnDemand' does not exist on type 'unknown'.
    178 |       const guild = guildResults[0];
    179 |
  > 180 |       const devSettings = guild?.settings?.devOnDemand;
        |                                            ^^^^^^^^^^^
    181 |       if (!devSettings?.enabled) {
    182 |         await interaction.reply({
    183 |           content: '❌ Developer-on-demand system is not enabled in this server.',

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:194:60
TS2769: No overload matches this call.
  Overload 1 of 2, '(value: { discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }): PgInsertBase<PgTableWithColumns<{ name: "users"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'preferences' does not exist in type '{ discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }'.
  Overload 2 of 2, '(values: { discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }[]): PgInsertBase<PgTableWithColumns<{ name: "users"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'discordId' does not exist in type '{ discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }[]'.
    192 |
    193 |       if (!user) {
  > 194 |         const newUserResults = await this.db.insert(users).values({
        |                                                            ^^^^^^
    195 |           discordId: interaction.user.id,
    196 |           username: interaction.user.username,
    197 |           preferences: { devRequests: [] },

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:202:48
TS2339: Property 'devRequests' does not exist on type 'unknown'.
    200 |       }
    201 |
  > 202 |       const activeRequests = user.preferences?.devRequests?.filter(
        |                                                ^^^^^^^^^^^
    203 |         (req: any) => ['open', 'assigned', 'in_progress'].includes(req.status)
    204 |       ) || [];
    205 |

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:227:42
TS2339: Property 'devRequests' does not exist on type 'unknown'.
    225 |       };
    226 |
  > 227 |       const requests = user.preferences?.devRequests || [];
        |                                          ^^^^^^^^^^^
    228 |       requests.push(request);
    229 |       const updatedPreferences = { ...user.preferences, devRequests: requests };
    230 |       await this.db.update(users).set({ preferences: updatedPreferences }).where(eq(users.id, user.id));

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:229:36
TS2698: Spread types may only be created from object types.
    227 |       const requests = user.preferences?.devRequests || [];
    228 |       requests.push(request);
  > 229 |       const updatedPreferences = { ...user.preferences, devRequests: requests };
        |                                    ^^^^^^^^^^^^^^^^^^^
    230 |       await this.db.update(users).set({ preferences: updatedPreferences }).where(eq(users.id, user.id));
    231 |
    232 |       // Post to request channel

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:230:41
TS2353: Object literal may only specify known properties, and 'preferences' does not exist in type '{ discordId?: string | SQL<unknown>; username?: string | SQL<unknown>; }'.
    228 |       requests.push(request);
    229 |       const updatedPreferences = { ...user.preferences, devRequests: requests };
  > 230 |       await this.db.update(users).set({ preferences: updatedPreferences }).where(eq(users.id, user.id));
        |                                         ^^^^^^^^^^^
    231 |
    232 |       // Post to request channel
    233 |       if (devSettings.requestChannel) {

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:300:44
TS2339: Property 'devOnDemand' does not exist on type 'unknown'.
    298 |       const guild = guildResults[0];
    299 |
  > 300 |       const devSettings = guild?.settings?.devOnDemand;
        |                                            ^^^^^^^^^^^
    301 |       if (!devSettings?.enabled) {
    302 |         await interaction.reply({
    303 |           content: '❌ Developer-on-demand system is not enabled in this server.',

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:314:44
TS2339: Property 'devRequests' does not exist on type 'unknown'.
    312 |
    313 |       allUsers.forEach(user => {
  > 314 |         const requests = user.preferences?.devRequests?.filter((req: any) => req.status === 'open') || [];
        |                                            ^^^^^^^^^^^
    315 |         allRequests.push(...requests);
    316 |       });
    317 |

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:389:39
TS2339: Property 'devRequests' does not exist on type 'unknown'.
    387 |
    388 |       for (const user of allUsers) {
  > 389 |         const req = user.preferences?.devRequests?.find((r: any) => r.id === requestId && r.status === 'open');
        |                                       ^^^^^^^^^^^
    390 |         if (req) {
    391 |           targetUser = user;
    392 |           request = req;

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:411:41
TS2353: Object literal may only specify known properties, and 'preferences' does not exist in type '{ discordId?: string | SQL<unknown>; username?: string | SQL<unknown>; }'.
    409 |       request.assignedAt = new Date().toISOString();
    410 |
  > 411 |       await this.db.update(users).set({ preferences: targetUser.preferences }).where(eq(users.id, targetUser.id));
        |                                         ^^^^^^^^^^^
    412 |
    413 |       // Notify client
    414 |       try {

ERROR in ./src/features/dev-on-demand/dev-on-demand.service.ts:456:43
TS2339: Property 'devRequests' does not exist on type 'unknown'.
    454 |       const user = userResults[0];
    455 |
  > 456 |       const requests = user?.preferences?.devRequests?.filter(
        |                                           ^^^^^^^^^^^
    457 |         (req: any) => ['open', 'assigned', 'in_progress'].includes(req.status)
    458 |       ) || [];
    459 |

ERROR in ./src/features/economy/economy.service.ts:53:60
TS2769: No overload matches this call.
  Overload 1 of 2, '(value: { discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }): PgInsertBase<PgTableWithColumns<{ name: "users"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'balance' does not exist in type '{ discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }'.
  Overload 2 of 2, '(values: { discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }[]): PgInsertBase<PgTableWithColumns<{ name: "users"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'discordId' does not exist in type '{ discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }[]'.
    51 |
    52 |       if (!user) {
  > 53 |         const newUserResults = await this.db.insert(users).values({
       |                                                            ^^^^^^
    54 |           discordId: interaction.user.id,
    55 |           username: interaction.user.username,
    56 |           balance: 0,

ERROR in ./src/features/economy/economy.service.ts:63:43
TS2339: Property 'lastDaily' does not exist on type 'unknown'.
    61 |
    62 |       const now = new Date();
  > 63 |       const lastDaily = user.preferences?.lastDaily ? new Date(user.preferences.lastDaily) : null;
       |                                           ^^^^^^^^^
    64 |       
    65 |       // Check if user already claimed today
    66 |       if (lastDaily && this.isSameDay(now, lastDaily)) {

ERROR in ./src/features/economy/economy.service.ts:63:81
TS2339: Property 'lastDaily' does not exist on type 'unknown'.
    61 |
    62 |       const now = new Date();
  > 63 |       const lastDaily = user.preferences?.lastDaily ? new Date(user.preferences.lastDaily) : null;
       |                                                                                 ^^^^^^^^^
    64 |       
    65 |       // Check if user already claimed today
    66 |       if (lastDaily && this.isSameDay(now, lastDaily)) {

ERROR in ./src/features/economy/economy.service.ts:79:38
TS2339: Property 'dailyStreak' does not exist on type 'unknown'.
    77 |
    78 |       // Calculate streak
  > 79 |       let streak = user.preferences?.dailyStreak || 0;
       |                                      ^^^^^^^^^^^
    80 |       if (lastDaily && this.isConsecutiveDay(now, lastDaily)) {
    81 |         streak += 1;
    82 |       } else {

ERROR in ./src/features/economy/economy.service.ts:94:9
TS2698: Spread types may only be created from object types.
    92 |       const newBalance = user.balance + totalReward;
    93 |       const updatedPreferences = {
  > 94 |         ...user.preferences,
       |         ^^^^^^^^^^^^^^^^^^^
    95 |         lastDaily: now.toISOString(),
    96 |         dailyStreak: streak,
    97 |       };

ERROR in ./src/features/economy/economy.service.ts:100:9
TS2353: Object literal may only specify known properties, and 'balance' does not exist in type '{ discordId?: string | SQL<unknown>; username?: string | SQL<unknown>; }'.
     98 |
     99 |       await this.db.update(users).set({ 
  > 100 |         balance: newBalance, 
        |         ^^^^^^^
    101 |         preferences: updatedPreferences 
    102 |       }).where(eq(users.id, user.id));
    103 |       

ERROR in ./src/features/leveling/leveling.service.ts:86:37
TS2769: No overload matches this call.
  Overload 1 of 2, '(value: { discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }): PgInsertBase<PgTableWithColumns<{ name: "users"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'experience' does not exist in type '{ discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }'.
  Overload 2 of 2, '(values: { discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }[]): PgInsertBase<PgTableWithColumns<{ name: "users"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'discordId' does not exist in type '{ discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }[]'.
    84 |
    85 |       if (!user) {
  > 86 |         await this.db.insert(users).values({
       |                                     ^^^^^^
    87 |           discordId: userId,
    88 |           username: 'Unknown',
    89 |           experience: amount,

ERROR in ./src/features/leveling/leveling.service.ts:101:43
TS2353: Object literal may only specify known properties, and 'experience' does not exist in type '{ discordId?: string | SQL<unknown>; username?: string | SQL<unknown>; }'.
     99 |         }
    100 |
  > 101 |         await this.db.update(users).set({ experience: newExperience }).where(eq(users.id, user.id));
        |                                           ^^^^^^^^^^
    102 |       }
    103 |     } catch (error) {
    104 |       this.logger.error(`Failed to add XP to user ${userId}:`, error);

ERROR in ./src/features/moderation/moderation.service.ts:273:60
TS2769: No overload matches this call.
  Overload 1 of 2, '(value: { discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }): PgInsertBase<PgTableWithColumns<{ name: "users"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'preferences' does not exist in type '{ discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }'.
  Overload 2 of 2, '(values: { discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }[]): PgInsertBase<PgTableWithColumns<{ name: "users"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'discordId' does not exist in type '{ discordId: string | SQL<unknown> | Placeholder<string, any>; username: string | SQL<unknown> | Placeholder<string, any>; }[]'.
    271 |
    272 |       if (!userEntity) {
  > 273 |         const newUserResults = await this.db.insert(users).values({
        |                                                            ^^^^^^
    274 |           discordId: user.id,
    275 |           username: user.username,
    276 |           preferences: { warnings: [] },

ERROR in ./src/features/moderation/moderation.service.ts:290:48
TS2339: Property 'warnings' does not exist on type 'unknown'.
    288 |       };
    289 |
  > 290 |       const warnings = userEntity.preferences?.warnings || [];
        |                                                ^^^^^^^^
    291 |       warnings.push(warning);
    292 |       
    293 |       const updatedPreferences = {

ERROR in ./src/features/moderation/moderation.service.ts:294:9
TS2698: Spread types may only be created from object types.
    292 |       
    293 |       const updatedPreferences = {
  > 294 |         ...userEntity.preferences,
        |         ^^^^^^^^^^^^^^^^^^^^^^^^^
    295 |         warnings,
    296 |       };
    297 |

ERROR in ./src/features/moderation/moderation.service.ts:298:41
TS2353: Object literal may only specify known properties, and 'preferences' does not exist in type '{ discordId?: string | SQL<unknown>; username?: string | SQL<unknown>; }'.
    296 |       };
    297 |
  > 298 |       await this.db.update(users).set({ preferences: updatedPreferences }).where(eq(users.id, userEntity.id));
        |                                         ^^^^^^^^^^^
    299 |
    300 |       this.logger.log(`${user.tag} was warned in ${interaction.guild.name} by ${interaction.user.tag}. Reason: ${reason}`);
    301 |

ERROR in ./src/features/moderation/moderation.service.ts:354:49
TS2339: Property 'warnings' does not exist on type 'unknown'.
    352 |       const userEntity = userResults[0];
    353 |
  > 354 |       const warnings = userEntity?.preferences?.warnings || [];
        |                                                 ^^^^^^^^
    355 |       const guildWarnings = warnings.filter((w: any) => w.guildId === interaction.guild!.id);
    356 |
    357 |       if (guildWarnings.length === 0) {

ERROR in ./src/features/role-access/role-access.service.ts:59:62
TS2769: No overload matches this call.
  Overload 1 of 2, '(value: { name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }): PgInsertBase<PgTableWithColumns<{ name: "guilds"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'settings' does not exist in type '{ name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }'.
  Overload 2 of 2, '(values: { name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }[]): PgInsertBase<PgTableWithColumns<{ name: "guilds"; schema: undefined; columns: { ...; }; dialect: "pg"; }>, NodePgQueryResultHKT, undefined, false, never>', gave the following error.
    Object literal may only specify known properties, and 'discordId' does not exist in type '{ name: string | SQL<unknown> | Placeholder<string, any>; discordId: string | SQL<unknown> | Placeholder<string, any>; }[]'.
    57 |
    58 |       if (!guild) {
  > 59 |         const newGuildResults = await this.db.insert(guilds).values({
       |                                                              ^^^^^^
    60 |           discordId: interaction.guild.id,
    61 |           name: interaction.guild.name,
    62 |           settings: { roleAccess: { enabled: false, tiers: [] } },

ERROR in ./src/features/role-access/role-access.service.ts:95:28
TS2339: Property 'roleAccess' does not exist on type 'unknown'.
    93 |
    94 |       // Initialize settings if not present
  > 95 |       if (!guild.settings?.roleAccess) {
       |                            ^^^^^^^^^^
    96 |         guild.settings = {
    97 |           ...guild.settings,
    98 |           roleAccess: {

ERROR in ./src/features/role-access/role-access.service.ts:97:11
TS2698: Spread types may only be created from object types.
     95 |       if (!guild.settings?.roleAccess) {
     96 |         guild.settings = {
  >  97 |           ...guild.settings,
        |           ^^^^^^^^^^^^^^^^^
     98 |           roleAccess: {
     99 |             enabled: false,
    100 |             tiers: [],

ERROR in ./src/features/role-access/role-access.service.ts:106:44
TS2353: Object literal may only specify known properties, and 'settings' does not exist in type '{ name?: string | SQL<unknown>; discordId?: string | SQL<unknown>; }'.
    104 |           },
    105 |         };
  > 106 |         await this.db.update(guilds).set({ settings: guild.settings }).where(eq(guilds.id, guild.id));
        |                                            ^^^^^^^^
    107 |       }
    108 |     } catch (error) {
    109 |       this.logger.error('Failed to set up access control:', error);

ERROR in ./src/features/role-access/role-access.service.ts:146:42
TS2339: Property 'roleAccess' does not exist on type 'unknown'.
    144 |       }
    145 |
  > 146 |       const roleAccess = guild.settings?.roleAccess || { tiers: [] };
        |                                          ^^^^^^^^^^
    147 |
    148 |       switch (action) {
    149 |         case 'create':

ERROR in ./src/features/role-access/role-access.service.ts:175:43
TS2698: Spread types may only be created from object types.
    173 |           });
    174 |
  > 175 |           const updatedSettingsCreate = { ...guild.settings, roleAccess };
        |                                           ^^^^^^^^^^^^^^^^^
    176 |           await this.db.update(guilds).set({ settings: updatedSettingsCreate }).where(eq(guilds.id, guild.id));
    177 |
    178 |           await interaction.reply({

ERROR in ./src/features/role-access/role-access.service.ts:176:46
TS2353: Object literal may only specify known properties, and 'settings' does not exist in type '{ name?: string | SQL<unknown>; discordId?: string | SQL<unknown>; }'.
    174 |
    175 |           const updatedSettingsCreate = { ...guild.settings, roleAccess };
  > 176 |           await this.db.update(guilds).set({ settings: updatedSettingsCreate }).where(eq(guilds.id, guild.id));
        |                                              ^^^^^^^^
    177 |
    178 |           await interaction.reply({
    179 |             content: `✅ **Access tier created successfully!**\n\n🏷️ **Tier:** ${name}\n👥 **Role:** ${role.name}\n\nUsers with the ${role.name} role now belong to the "${name}" tier.`,

ERROR in ./src/features/role-access/role-access.service.ts:203:43
TS2698: Spread types may only be created from object types.
    201 |
    202 |           roleAccess.tiers.splice(tierIndex, 1);
  > 203 |           const updatedSettingsDelete = { ...guild.settings, roleAccess };
        |                                           ^^^^^^^^^^^^^^^^^
    204 |           await this.db.update(guilds).set({ settings: updatedSettingsDelete }).where(eq(guilds.id, guild.id));
    205 |
    206 |           await interaction.reply({

ERROR in ./src/features/role-access/role-access.service.ts:204:46
TS2353: Object literal may only specify known properties, and 'settings' does not exist in type '{ name?: string | SQL<unknown>; discordId?: string | SQL<unknown>; }'.
    202 |           roleAccess.tiers.splice(tierIndex, 1);
    203 |           const updatedSettingsDelete = { ...guild.settings, roleAccess };
  > 204 |           await this.db.update(guilds).set({ settings: updatedSettingsDelete }).where(eq(guilds.id, guild.id));
        |                                              ^^^^^^^^
    205 |
    206 |           await interaction.reply({
    207 |             content: `✅ **Access tier "${name}" deleted successfully!**`,

ERROR in ./src/features/role-access/role-access.service.ts:264:43
TS2339: Property 'roleAccess' does not exist on type 'unknown'.
    262 |       const guild = guildResults[0];
    263 |
  > 264 |       const roleAccess = guild?.settings?.roleAccess || { enabled: false, tiers: [] };
        |                                           ^^^^^^^^^^
    265 |
    266 |       const embed = new EmbedBuilder()
    267 |         .setColor(roleAccess.enabled ? 0x10B981 : 0x6B7280)

ERROR in ./src/features/role-access/role-access.service.ts:333:43
TS2339: Property 'roleAccess' does not exist on type 'unknown'.
    331 |       const guild = guildResults[0];
    332 |
  > 333 |       const roleAccess = guild?.settings?.roleAccess;
        |                                           ^^^^^^^^^^
    334 |       if (!roleAccess?.enabled || !roleAccess.tiers) return;
    335 |
    336 |       // Determine new tier based on roles

ERROR in ./src/features/role-access/role-access.service.ts:365:43
TS2339: Property 'roleAccess' does not exist on type 'unknown'.
    363 |       const guild = guildResults[0];
    364 |
  > 365 |       const roleAccess = guild?.settings?.roleAccess;
        |                                           ^^^^^^^^^^
    366 |       if (!roleAccess?.enabled || !roleAccess.tiers) return [];
    367 |
    368 |       const discordGuild = await guild; // Would need Discord client here

Found 114 errors in 7292 ms.
